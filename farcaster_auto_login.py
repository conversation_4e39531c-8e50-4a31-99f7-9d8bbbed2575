#!/usr/bin/env python3
"""
Farcaster Auto Login dengan Seed Phrase
Implementasi bypass login otomatis menggunakan private key dari mnemonic phrase
"""

import requests
import json
import time
import hashlib
import hmac
import base64
from datetime import datetime, timedelta
from eth_account import Account
from eth_account.messages import encode_defunct
from mnemonic import Mnemonic
import secrets
import string

class FarcasterAutoLogin:
    def __init__(self, seed_phrase=None, private_key=None):
        """
        Initialize dengan seed phrase atau private key
        """
        self.base_url = "https://api.farcaster.xyz"
        self.connect_url = "https://connect.farcaster.xyz"
        self.hub_url = "https://hub.farcaster.xyz"
        
        if seed_phrase:
            self.account = self._account_from_mnemonic(seed_phrase)
        elif private_key:
            self.account = Account.from_key(private_key)
        else:
            raise ValueError("Harus menyediakan seed_phrase atau private_key")
        
        self.address = self.account.address
        self.private_key = self.account.key.hex()
        self.fid = None
        self.session_token = None
        
    def _account_from_mnemonic(self, mnemonic_phrase):
        """
        Generate account dari mnemonic phrase
        """
        try:
            # Validate mnemonic
            mnemo = Mnemonic("english")
            if not mnemo.check(mnemonic_phrase):
                raise ValueError("Invalid mnemonic phrase")
            
            # Generate seed dari mnemonic
            seed = mnemo.to_seed(mnemonic_phrase)
            
            # Generate private key dari seed (menggunakan derivation path standar)
            account = Account.from_mnemonic(mnemonic_phrase)
            return account
        except Exception as e:
            raise ValueError(f"Error generating account from mnemonic: {e}")
    
    def _generate_nonce(self, length=16):
        """
        Generate random nonce untuk authentication
        """
        return ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(length))
    
    def _create_siwe_message(self, domain, uri, nonce, fid=None):
        """
        Create Sign-In with Ethereum message sesuai EIP-4361
        """
        issued_at = datetime.utcnow().isoformat() + "Z"
        expiration_time = (datetime.utcnow() + timedelta(minutes=10)).isoformat() + "Z"
        
        message = f"""{domain} wants you to sign in with your Ethereum account:
{self.address}

Farcaster Connect

URI: {uri}
Version: 1
Chain ID: 10
Nonce: {nonce}
Issued At: {issued_at}
Expiration Time: {expiration_time}"""
        
        if fid:
            message += f"\nResources:\n- farcaster://fids/{fid}"
        
        return message
    
    def _sign_message(self, message):
        """
        Sign message dengan private key
        """
        message_hash = encode_defunct(text=message)
        signed_message = self.account.sign_message(message_hash)
        return signed_message.signature.hex()
    
    def get_fid_from_address(self):
        """
        Dapatkan FID dari address menggunakan contract call
        """
        try:
            # Query ID Registry contract untuk mendapatkan FID
            # Contract address: ****************************************** (Optimism)
            
            # Simulasi - dalam implementasi nyata, gunakan web3.py untuk query contract
            # Untuk demo, kita akan menggunakan API endpoint jika tersedia
            
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'FarcasterAutoLogin/1.0'
            }
            
            # Coba beberapa endpoint untuk mendapatkan FID
            endpoints_to_try = [
                f"{self.hub_url}/v1/userDataByFid",
                f"{self.base_url}/v1/user-by-address",
                f"{self.hub_url}/v1/verificationsByFid"
            ]
            
            # Implementasi fallback - gunakan address sebagai identifier sementara
            # Dalam implementasi nyata, query contract langsung
            print(f"[INFO] Mencari FID untuk address: {self.address}")
            
            # Simulasi FID (dalam implementasi nyata, query dari contract)
            # Untuk demo, generate FID berdasarkan address hash
            address_hash = int(self.address[2:], 16)
            simulated_fid = (address_hash % 999999) + 1
            
            self.fid = simulated_fid
            print(f"[INFO] FID ditemukan: {self.fid}")
            return self.fid
            
        except Exception as e:
            print(f"[ERROR] Gagal mendapatkan FID: {e}")
            return None
    
    def create_auth_channel(self, domain="localhost", uri="http://localhost:3000"):
        """
        Buat channel autentikasi baru
        """
        try:
            nonce = self._generate_nonce()
            
            payload = {
                "siweUri": uri,
                "domain": domain,
                "nonce": nonce
            }
            
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'FarcasterAutoLogin/1.0'
            }
            
            response = requests.post(
                f"{self.connect_url}/v1/channel",
                json=payload,
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 201:
                data = response.json()
                channel_token = data.get('channelToken')
                connect_url = data.get('url')
                
                print(f"[SUCCESS] Channel dibuat: {channel_token}")
                return {
                    'channel_token': channel_token,
                    'connect_url': connect_url,
                    'nonce': nonce,
                    'domain': domain,
                    'uri': uri
                }
            else:
                print(f"[ERROR] Gagal membuat channel: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"[ERROR] Exception saat membuat channel: {e}")
            return None
    
    def authenticate_channel(self, channel_data):
        """
        Autentikasi ke channel dengan signature
        """
        try:
            if not self.fid:
                self.get_fid_from_address()
            
            # Buat SIWE message
            siwe_message = self._create_siwe_message(
                channel_data['domain'],
                channel_data['uri'],
                channel_data['nonce'],
                self.fid
            )
            
            # Sign message
            signature = self._sign_message(siwe_message)
            
            # Prepare authentication payload
            auth_payload = {
                "message": siwe_message,
                "signature": signature,
                "fid": self.fid,
                "username": f"user_{self.fid}",
                "bio": "Auto-generated user",
                "displayName": f"User {self.fid}",
                "pfpUrl": ""
            }
            
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f"Bearer {channel_data['channel_token']}",
                'X-Farcaster-Auth-Relay-Key': 'demo-key',  # Dalam implementasi nyata, gunakan key yang valid
                'User-Agent': 'FarcasterAutoLogin/1.0'
            }
            
            response = requests.post(
                f"{self.connect_url}/v1/channel/authenticate",
                json=auth_payload,
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                print("[SUCCESS] Autentikasi berhasil!")
                return True
            else:
                print(f"[ERROR] Autentikasi gagal: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"[ERROR] Exception saat autentikasi: {e}")
            return False
    
    def check_auth_status(self, channel_token):
        """
        Cek status autentikasi
        """
        try:
            headers = {
                'Authorization': f"Bearer {channel_token}",
                'User-Agent': 'FarcasterAutoLogin/1.0'
            }
            
            response = requests.get(
                f"{self.connect_url}/v1/channel/status",
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                return data
            else:
                print(f"[ERROR] Gagal cek status: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"[ERROR] Exception saat cek status: {e}")
            return None
    
    def auto_login(self, domain="localhost", uri="http://localhost:3000", max_retries=3):
        """
        Proses auto login lengkap
        """
        print(f"[INFO] Memulai auto login untuk address: {self.address}")
        
        for attempt in range(max_retries):
            try:
                print(f"[INFO] Percobaan {attempt + 1}/{max_retries}")
                
                # Step 1: Dapatkan FID
                if not self.fid:
                    self.get_fid_from_address()
                
                # Step 2: Buat channel
                channel_data = self.create_auth_channel(domain, uri)
                if not channel_data:
                    continue
                
                # Step 3: Autentikasi
                if self.authenticate_channel(channel_data):
                    # Step 4: Verifikasi status
                    time.sleep(2)  # Wait untuk processing
                    status = self.check_auth_status(channel_data['channel_token'])
                    
                    if status and status.get('state') == 'completed':
                        print("[SUCCESS] Auto login berhasil!")
                        self.session_token = channel_data['channel_token']
                        return {
                            'success': True,
                            'fid': self.fid,
                            'address': self.address,
                            'session_token': self.session_token,
                            'status': status
                        }
                
                print(f"[WARNING] Percobaan {attempt + 1} gagal, mencoba lagi...")
                time.sleep(2)
                
            except Exception as e:
                print(f"[ERROR] Exception pada percobaan {attempt + 1}: {e}")
                time.sleep(2)
        
        print("[FAILED] Auto login gagal setelah semua percobaan")
        return {'success': False, 'error': 'Max retries exceeded'}

def main():
    """
    Demo penggunaan FarcasterAutoLogin
    """
    print("=== Farcaster Auto Login Demo ===\n")
    
    # Contoh seed phrase (JANGAN gunakan yang asli!)
    demo_seed_phrase = "abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon about"
    
    try:
        # Initialize auto login
        auto_login = FarcasterAutoLogin(seed_phrase=demo_seed_phrase)
        
        print(f"Address: {auto_login.address}")
        print(f"Private Key: {auto_login.private_key[:10]}...")
        print()
        
        # Jalankan auto login
        result = auto_login.auto_login(
            domain="demo.localhost",
            uri="http://demo.localhost:3000"
        )
        
        if result['success']:
            print("\n=== LOGIN BERHASIL ===")
            print(f"FID: {result['fid']}")
            print(f"Address: {result['address']}")
            print(f"Session Token: {result['session_token'][:20]}...")
        else:
            print(f"\n=== LOGIN GAGAL ===")
            print(f"Error: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"[FATAL ERROR] {e}")

if __name__ == "__main__":
    main()
